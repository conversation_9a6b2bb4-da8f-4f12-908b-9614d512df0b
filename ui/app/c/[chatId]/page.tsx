'use client';

import { Suspense } from 'react';
import dynamic from 'next/dynamic';
import Layout from '@/components/Layout';

// 动态导入 ChatWindow 组件，禁用 SSR
const ChatWindow = dynamic(() => import('@/components/ChatWindow'), {
  ssr: false,
  loading: () => <div className="flex items-center justify-center h-screen">Loading...</div>
});

const Page = ({ params }: { params: { chatId: string } }) => {
  return (
    <Layout>
      <Suspense fallback={<div>Loading...</div>}>
        <ChatWindow id={params.chatId}/>
      </Suspense>
    </Layout>
  );
};

export default Page;
